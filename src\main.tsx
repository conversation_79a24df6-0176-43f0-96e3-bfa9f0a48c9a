import { createRoot } from 'react-dom/client'
import App from './App.tsx'
import './index.css'
import { registerServiceWorker } from './lib/register-service-worker'

// Development debugging scripts removed for cleaner console output

// Ensure DOM is ready before rendering
function initializeApp() {
  try {
    // Clean development console output
    if (import.meta.env.DEV) {
      // Store original console methods
      const originalWarn = console.warn;
      const originalError = console.error;
      const originalLog = console.log;
      const originalInfo = console.info;
      const originalDebug = console.debug;

      // Enhanced patterns to filter out development noise
      const filterPatterns = [
        // React DevTools messages
        'React DevTools', 'Download the React DevTools', 'react-devtools',
        'fb.me/react-devtools', 'reactjs.org/link/react-devtools',
        'Install React Developer Tools', 'React Developer Tools',

        // Browser extension messages - Comprehensive filtering
        'Teflon Content Script', 'contentscript.bundle.js', 'chunk-R6S4VRB5.js',
        'Extension context', 'background.js', 'message port closed',
        'Frame with ID', 'No tab with id', 'Unchecked runtime.lastError',
        'Could not establish connection', 'Receiving end does not exist',
        'chrome-extension://', 'moz-extension://', 'safari-extension://', 'edge-extension://',
        'The message port closed before a response was received',
        'Uncaught (in promise)', 'runtime.lastError', 'Extension context invalidated',
        'Cannot access contents of', 'Script error', 'Non-Error promise rejection captured',
        // Additional patterns for contentscript.bundle.js errors
        'contentscript.bundle.js:1', 'contentscript.bundle.js:2', 'contentscript.bundle.js:3',
        'contentscript', 'content script', 'content_script', 'extension script',
        'browser extension', 'extension error', 'chrome extension',

        // Service Worker development messages
        'Service Worker:', 'Service worker', 'SW:', 'service-worker',
        'Development mode detected', 'minimal functionality enabled',
        'Service Worker registration skipped', 'SW registration',

        // Performance monitoring output
        'LCP:', 'FID:', 'CLS:', 'LargestContentfulPaint', 'FirstInputDelay', 'CumulativeLayoutShift',
        'PerformanceObserver', 'Performance entry', 'Core Web Vitals',

        // Analytics and tracking
        '_vercel/speed-insights', 'Speed Insights', 'Analytics',
        'Vercel Analytics', 'Web Vitals',

        // Image loading and verification messages
        'Image URL encoding', 'Image loading verification', 'Image test',
        'URL encoding test', 'Image optimization', 'LQIP generation',

        // Development build messages
        'HMR update', 'Hot reload', '[vite]', '[HMR]',
        'Development build', 'Dev server'
      ];

      const shouldFilter = (message: string) => {
        return filterPatterns.some(pattern => message.includes(pattern));
      };

      // Enhanced console filtering with better error handling
      console.warn = (...args) => {
        try {
          const message = args.join(' ');
          if (!shouldFilter(message)) {
            originalWarn.apply(console, args);
          }
        } catch (error) {
          // Fallback to original if filtering fails
          originalWarn.apply(console, args);
        }
      };

      console.error = (...args) => {
        try {
          const message = args.join(' ');
          if (!shouldFilter(message) && !(message.includes('Service Worker') && message.includes('localhost'))) {
            originalError.apply(console, args);
          }
        } catch (error) {
          // Fallback to original if filtering fails
          originalError.apply(console, args);
        }
      };

      console.log = (...args) => {
        try {
          const message = args.join(' ');
          if (!shouldFilter(message)) {
            originalLog.apply(console, args);
          }
        } catch (error) {
          // Fallback to original if filtering fails
          originalLog.apply(console, args);
        }
      };

      // Filter console.info
      console.info = (...args) => {
        try {
          const message = args.join(' ');
          if (!shouldFilter(message)) {
            originalInfo.apply(console, args);
          }
        } catch (error) {
          // Fallback to original if filtering fails
          originalInfo.apply(console, args);
        }
      };

      // Filter console.debug
      console.debug = (...args) => {
        try {
          const message = args.join(' ');
          if (!shouldFilter(message)) {
            originalDebug.apply(console, args);
          }
        } catch (error) {
          // Fallback to original if filtering fails
          originalDebug.apply(console, args);
        }
      };

      // Enhanced handler for unhandled promise rejections and errors from browser extensions
      const handleExtensionError = (event: any) => {
        // Extract message from various possible sources
        const message = event.reason?.message || event.message || event.error?.message || event.reason || '';
        const stack = event.reason?.stack || event.error?.stack || '';
        const filename = event.filename || event.error?.filename || '';

        // Combine all sources for comprehensive filtering
        const fullErrorText = `${message} ${stack} ${filename}`.toLowerCase();

        // Check if this is an extension-related error
        if (typeof message === 'string' && shouldFilter(message)) {
          event.preventDefault();
          return;
        }

        // Additional check for contentscript.bundle.js specifically
        if (fullErrorText.includes('contentscript.bundle.js') ||
            fullErrorText.includes('message port closed') ||
            fullErrorText.includes('extension context')) {
          event.preventDefault();
          return;
        }
      };

      window.addEventListener('unhandledrejection', handleExtensionError);
      window.addEventListener('error', handleExtensionError);

      // Optional: Log that console filtering is active (can be removed if desired)
      // originalLog('🧹 Development console filtering active - hiding extension and DevTools messages');
    }

    // Apply performance optimizations
    document.documentElement.classList.add('no-animations');

    // Remove the class once page is loaded to restore animations
    window.addEventListener('load', () => {
      // Minor delay to ensure everything is rendered
      setTimeout(() => {
        document.documentElement.classList.remove('no-animations');
      }, 300);
    });

    // Register service worker for caching and offline capabilities
    registerServiceWorker();

    // Get root element and ensure it exists
    const rootElement = document.getElementById("root");
    if (!rootElement) {
      throw new Error('Root element not found');
    }

    // Render the application
    const root = createRoot(rootElement);
    root.render(<App />);

  } catch (error) {
    console.error('Failed to initialize app:', error);
    // Fallback error display
    const rootElement = document.getElementById("root");
    if (rootElement) {
      rootElement.innerHTML = `
        <div style="display: flex; align-items: center; justify-content: center; min-height: 100vh; font-family: system-ui;">
          <div style="text-align: center; padding: 2rem;">
            <h1 style="color: #ef4444; margin-bottom: 1rem;">Application Error</h1>
            <p style="color: #6b7280; margin-bottom: 1rem;">Failed to load the application. Please refresh the page.</p>
            <button onclick="window.location.reload()" style="background: #3b82f6; color: white; padding: 0.5rem 1rem; border: none; border-radius: 0.375rem; cursor: pointer;">
              Refresh Page
            </button>
          </div>
        </div>
      `;
    }
  }
}

// Initialize when DOM is ready
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', initializeApp);
} else {
  // DOM is already ready
  initializeApp();
}
